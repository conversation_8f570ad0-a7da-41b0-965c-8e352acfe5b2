import type { MainAppProps } from '#/main';
import 'uno.css';
import '/@/design/index.less';
import 'ant-design-vue/dist/reset.css';
// 注册图标
import 'virtual:svg-icons-register';

import App from './App.vue';
import { createApp } from 'vue';
import { initAppConfigStore } from '/@/logics/initAppConfig';
import { setupErrorHandle } from '/@/logics/error-handle';
import { router, createRouter, setupRouter } from '/@/router';
import { setupRouterGuard } from '/@/router/guard';
import { setupStore } from '/@/store';
import { setupGlobDirectives } from '/@/directives';
import { setupI18n } from '/@/locales/setupI18n';
import { registerGlobComp } from '/@/components/registerGlobComp';
// 保留按需注册第三方组件的能力
import { registerThirdComp } from '/@/settings/registerThirdComp';

//import { useSso } from '/@/hooks/web/useSso';
import { useAppStoreWithOut } from '@/store/modules/app';
// 导入配置验证和网络测试工具
// 已移除开发/调试类工具以精简首屏

// Online 模块已移除
// 资源预加载工具
// 缓存管理器
import { initCacheEventManager, setupPageCacheSync } from '/@/utils/cacheEventManager';
import { startCacheCleanup } from '/@/utils/itemGroupRelationManager';

// 程序入口
async function main() {
  // 获取参数
  const props = getMainAppProps();
  // 普通启动
  await bootstrap(props);
}

main();

async function bootstrap(props?: MainAppProps) {
  // 创建应用实例
  const app = createApp(App);
  // 【QQYUN-6329】
  window['JAppRootInstance'] = app;
  // 创建路由
  createRouter();
  // 配置存储
  setupStore(app);
  // 串行初始化（极简）
  await setupI18n(app);
  initAppConfigStore();
  // 配置参数（在 store 完全初始化后）
  setupProps(props);
  // Online 模块已移除
  registerGlobComp(app);

  //await useSso().ssoLogin();
  setupRouter(app);
  setupRouterGuard(router);
  setupGlobDirectives(app);
  setupErrorHandle(app);

  // 当路由准备好时再执行挂载( https://next.router.vuejs.org/api/#isready)
  await router.isReady();

  // 挂载应用
  app.mount(getMountContainer(props), true);

  // 路由就绪、应用可见后，再延后注册第三方组件，减少挂载前同步等待
  // 不阻塞首屏交互
  setTimeout(() => {
    void registerThirdComp(app);
  }, 0);

  // 初始化缓存管理器
  initCacheEventManager();
  setupPageCacheSync();
  startCacheCleanup();

  return app;
}

// 获取应用挂载容器
function getMountContainer(props?: MainAppProps) {
  const id = '#app';
  if (!props?.container?.querySelector) {
    return id;
  }
  return props.container.querySelector(id) ?? id;
}

// 获取主应用参数
function getMainAppProps(): MainAppProps {
  // 从 queryString 中获取
  const searchParams = new URLSearchParams(window.location.search);
  // 隐藏侧边栏（菜单）
  const hideSider = searchParams.get('hideSider') === 'true';
  // 隐藏顶部
  const hideHeader = searchParams.get('hideHeader') === 'true';
  // 隐藏 多Tab 切换
  const hideMultiTabs = searchParams.get('hideMultiTabs') === 'true';

  return {
    hideSider,
    hideHeader,
    hideMultiTabs,
  };
}

// 配置主应用参数
function setupProps(props?: MainAppProps) {
  if (!props) {
    return;
  }
  try {
    const appStore = useAppStoreWithOut();
    appStore.setMainAppProps(props);
  } catch (error) {
    console.error('Failed to setup app props:', error);
    // 如果 store 未初始化，可以稍后重试或使用默认值
  }
}
