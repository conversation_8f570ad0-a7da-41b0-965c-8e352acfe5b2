<script setup lang="ts">
  import type { PromptsProps } from 'ant-design-x-vue';
  import { Prompts, Sender } from 'ant-design-x-vue';
  import { computed, h, ref, nextTick, onMounted, onUnmounted } from 'vue';
  import { FireOutlined, ReadOutlined, SettingOutlined, StopOutlined, CheckOutlined } from '@ant-design/icons-vue';
  import { theme, <PERSON>lide<PERSON>, Drawer, Button, Space, Tooltip } from 'ant-design-vue';
  import signMd5Utils from '@/utils/encryption/signMd5Utils';
  import { getToken } from '@/utils/auth';
  import { marked } from 'marked';
  import DOMPurify from 'dompurify';
  import { fetchEventSource, EventSourceMessage } from '@microsoft/fetch-event-source';

  const props = defineProps({
    modId: {
      type: String,
      default: '1888072265078280195', // 默认模型
    },
    defaultOutputSpeed: {
      type: Number,
      default: 30, // 每秒输出的字符数，可根据需要调整
    },
    defaultSentenceDelay: {
      type: Number,
      default: 300, // 句子之间的延迟(毫秒)
    },
    defaultParagraphDelay: {
      type: Number,
      default: 700, // 段落之间的延迟(毫秒)
    },
  });

  const { token } = theme.useToken();

  const styles = computed(() => {
    const fontFamilyCode = "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace";
    return {
      layout: {
        width: '100%',
        height: '722px',
        'border-radius': `${token.value.borderRadius}px`,
        display: 'flex',
        background: `${token.value.colorBgContainer}`,
        'font-family': `AlibabaPuHuiTi, ${token.value.fontFamily}, sans-serif`,
      },
      chat: {
        height: '100%',
        width: '100%',
        margin: '0 auto',
        'box-sizing': 'border-box',
        display: 'flex',
        'flex-direction': 'column',
        padding: '4px',
        gap: '4px',
      },
      headerControls: {
        display: 'flex',
        justifyContent: 'flex-end',
        marginBottom: '4px',
      },
      messagesContainer: {
        flex: 1,
        overflowY: 'auto',
        paddingRight: '10px',
        marginBottom: '2px',
      },
      messageItem: {
        marginBottom: '8px',
        display: 'flex',
        alignItems: 'flex-end',
        position: 'relative',
      },
      messageContent: {
        padding: '10px 15px 6px',
        maxWidth: '70%',
        color: token.value.colorText,
        wordWrap: 'break-word',
        whiteSpace: 'pre-wrap',
      },
      messageContentUser: {
        marginLeft: 'auto',
        backgroundColor: token.value.colorPrimaryBg,
        borderRadius: '16px 16px 4px 16px',
      },
      messageContentAssistant: {
        marginRight: 'auto',
        backgroundColor: token.value.colorBgElevated,
        borderRadius: '16px 16px 16px 4px',
      },
      actionButton: {
        marginLeft: '8px',
        marginTop: '4px',
      },
      messageActions: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-end',
        marginLeft: '8px',
      },
      codeStyle: {
        backgroundColor: token.value.colorFillTertiary,
        padding: '0.2em 0.4em',
        borderRadius: '4px',
        fontSize: '1em',
        fontFamily: token.value.fontFamily || fontFamilyCode,
      },
      preStyle: {
        backgroundColor: token.value.colorFillTertiary,
        padding: '1em',
        borderRadius: '8px',
        overflowX: 'auto',
        fontSize: '1em',
        fontFamily: token.value.fontFamily || fontFamilyCode,
        margin: '0.5em 0',
      },
      loadingIndicator: {
        marginLeft: '8px',
        color: token.value.colorTextDescription,
      },
      placeholder: {
        paddingTop: '6px',
        textAlign: 'left',
        flex: 1,
        color: token.value.colorTextDescription,
      },
      sender: {
        boxShadow: token.value.boxShadow,
        marginBottom: '0px',
      },
      logo: {
        display: 'flex',
        height: '72px',
        'align-items': 'center',
        'justify-content': 'start',
        padding: '0 24px',
        'box-sizing': 'border-box',
      },
      'logo-img': {
        width: '24px',
        height: '24px',
        display: 'inline-block',
      },
      'logo-span': {
        display: 'inline-block',
        margin: '0 8px',
        'font-weight': 'bold',
        color: token.value.colorText,
        'font-size': '16px',
      },
      addBtn: {
        background: '#1677ff0f',
        border: '1px solid #1677ff34',
        width: 'calc(100% - 24px)',
        margin: '0 12px 24px 12px',
      },
      senderWrapper: {
        width: '100%',
        position: 'relative',
      },
      stopButton: {
        position: 'absolute',
        right: '10px',
        bottom: '60px',
        zIndex: 10,
      },
      controlsWrapper: {
        display: 'flex',
        justifyContent: 'flex-end',
        marginBottom: '4px',
      },
      promptsRow: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '1px',
      },
      promptsContainer: {
        flex: '1',
      },
      controlsContainer: {
        display: 'flex',
        justifyContent: 'flex-end',
        marginLeft: '8px',
      },
      senderCompact: {
        boxShadow: token.value.boxShadow,
        marginBottom: '0',
        marginTop: '0',
        paddingTop: '0',
        paddingBottom: '0',
        height: 'auto',
      },
    } as const;
  });

  defineOptions({ name: 'AiAdviceChat' });

  const senderPromptsItems: PromptsProps['items'] = [
    {
      key: '1',
      description: '重新生成',
      icon: h(FireOutlined, { style: { color: '#FF4D4F' } }),
    },
    {
      key: '2',
      description: '丰富建议',
      icon: h(ReadOutlined, { style: { color: '#1890FF' } }),
    },
  ];

  const content = ref('');
  const agentRequestLoading = ref(false);
  const messagesContainerRef = ref<HTMLElement | null>(null);
  const userScrolled = ref(false); // 追踪用户是否手动滚动
  const autoScrollEnabled = ref(true); // 控制是否启用自动滚动
  const scrollThreshold = 100; // 接近底部的阈值（像素）

  interface ChatMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    loading?: boolean;
  }

  interface AiSummaryRequestBody {
    messages: { role: 'user' | 'assistant'; content: string }[];
    modId: string;
  }

  const messages = ref<ChatMessage[]>([]);
  interface UseXAgentCallbacks {
    onSuccess: (finalResult: string) => void;
    onUpdate: (chunk: string) => void;
    onError: (error: Error) => void;
  }

  // 流式输出控制逻辑
  const textBuffer = ref('');
  const outputInterval = ref<number | null>(null);
  const outputQueue = ref<{ text: string; type: 'text' | 'sentence' | 'paragraph' }[]>([]);

  function processTextForOutput(text: string): { text: string; type: 'text' | 'sentence' | 'paragraph' }[] {
    // 确保输入文本中不包含HTML标签
    const cleanedText = text.replace(/<br\s*\/?>/g, '\n').replace(/<\/br>/g, '\n');
    
    // 将文本分解为更小的单位进行处理
    const result: { text: string; type: 'text' | 'sentence' | 'paragraph' }[] = [];

    // 先按段落分割
    const paragraphs = cleanedText.split(/\n\n+/);

    paragraphs.forEach((paragraph, pIndex) => {
      // 每个段落按句子分割
      const sentences = paragraph.split(/([.!?。！？]\s*)/g);

      let currentSentence = '';
      sentences.forEach((part, sIndex) => {
        currentSentence += part;

        // 如果这部分是句子的结束符号
        if (part.match(/[.!?。！？]\s*/)) {
          result.push({ text: currentSentence, type: 'sentence' });
          currentSentence = '';
        }
      });

      // 处理最后可能剩余的不完整句子
      if (currentSentence) {
        result.push({ text: currentSentence, type: 'sentence' });
      }

      // 在段落结束添加段落标记和换行符
      if (pIndex < paragraphs.length - 1) {
        result.push({ text: '\n\n', type: 'paragraph' });
      }
    });

    return result;
  }

  function startOutputProcess(messageId: string) {
    // 清除可能存在的之前的间隔
    if (outputInterval.value !== null) {
      clearInterval(outputInterval.value);
    }

    // 一个变量来跟踪上次暂停的时间
    let lastPauseTime = 0;

    outputInterval.value = window.setInterval(
      () => {
        // 如果队列为空，清除间隔
        if (outputQueue.value.length === 0) {
          if (outputInterval.value !== null) {
            clearInterval(outputInterval.value);
            outputInterval.value = null;
          }
          return;
        }

        const now = Date.now();
        const timeSinceLastPause = now - lastPauseTime;

        // 获取队列中的第一项
        const item = outputQueue.value[0];

        // 根据类型决定是否需要暂停
        if (item.type === 'sentence' && timeSinceLastPause < sentenceDelay.value) {
          return; // 等待句子延迟
        } else if (item.type === 'paragraph' && timeSinceLastPause < paragraphDelay.value) {
          return; // 等待段落延迟
        }

        // 从队列中移除这一项
        outputQueue.value.shift();

        // 添加到缓冲区
        textBuffer.value += item.text;

        // 更新消息内容
        const msgIndex = messages.value.findIndex((m) => m.id === messageId);
        if (msgIndex !== -1) {
          messages.value[msgIndex].content = textBuffer.value;
          // 不需要在这里调用renderMarkdown，因为v-html指令会处理
          nextTick(scrollToBottom);
        }

        // 如果是句子或段落结束，设置暂停时间
        if (item.type === 'sentence' || item.type === 'paragraph') {
          lastPauseTime = now;
        }
      },
      1000 / (outputSpeed.value / 3)
    ); // 调整速率，由于我们以块处理，所以除以3来近似每个字符
  }

  function addToOutputQueue(text: string, messageId: string) {
    // 处理文本，准备添加到输出队列
    const processedItems = processTextForOutput(text);
    outputQueue.value.push(...processedItems);

    // 如果输出过程尚未开始，则启动
    if (outputInterval.value === null) {
      startOutputProcess(messageId);
    }
  }

  function clearOutputProcess() {
    if (outputInterval.value !== null) {
      clearInterval(outputInterval.value);
      outputInterval.value = null;
    }
    outputQueue.value = [];
    textBuffer.value = '';
  }

  // 当前请求的取消处理函数
  const currentRequestCancelHandler = ref<(() => void) | null>(null);

  function useXAgentWithFetchEventSource() {
    const agentRequestLoading = ref(false);
    let currentAbortController: AbortController | null = null;

    const request = (requestBody: AiSummaryRequestBody, callbacks: UseXAgentCallbacks) => {
      agentRequestLoading.value = true;
      currentAbortController = new AbortController();
      const abortController = currentAbortController; // Capture the controller for this specific request

      const sign = signMd5Utils.getSign('/summary/customerRegSummary/ai/streamSummary', null);
      const token = getToken();
      let accumulatedData = '';
      let streamClosed = false;
      let hasReceivedData = false; // 标记是否收到了任何数据

      // 重置输出控制
      clearOutputProcess();

      fetchEventSource('http://localhost:8090/jeecgboot/summary/customerRegSummary/ai/streamSummary', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'text/event-stream',
          'X-Sign': sign,
          'X-TIMESTAMP': String(signMd5Utils.getTimestamp()),
          'X-Access-Token': token || '',
        },
        body: JSON.stringify(requestBody),
        signal: abortController.signal,

        async onopen(response) {
          if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
            console.log('[fetchEventSource] Connection opened');
          } else {
            const errorText = await response.text();
            throw new Error(`[fetchEventSource] Failed to connect: ${response.status} - ${errorText || response.statusText}`);
          }
        },

        onmessage(msg: EventSourceMessage) {
          console.log(`[fetchEventSource] Received event: ${msg.event}, data: ${msg.data}`);
          //将msg.data中的各种br标签替换为\n
          const data = msg.data.replace(/<br\s*\/?>/g, '\n').replace(/<\/br>/g, '\n');
          console.log('[fetchEventSource] data:', data);

          if (streamClosed) {
            console.warn('[fetchEventSource] Ignoring message received after stream closure signal.');
            return;
          }

          // 标记已经收到了数据
          hasReceivedData = true;

          // 根据后端实际发送的事件类型处理
          if (msg.event === 'update' || msg.event === 'message') {
            if (msg.data) {
              accumulatedData += data; // 这里data已经是清理过的
              callbacks.onUpdate(data);
            }
          } else if (msg.event === 'done') {
            // 标记流已关闭，但不立即结束处理
            streamClosed = true;
            
            // 最终确保累积的数据不包含HTML标签
            accumulatedData = accumulatedData.replace(/<br\s*\/?>/g, '\n').replace(/<\/br>/g, '\n');

            // 检查输出队列是否为空
            if (outputQueue.value.length === 0 && textBuffer.value === accumulatedData) {
              // 如果队列为空且已经显示了所有文本，立即完成
              agentRequestLoading.value = false;
              callbacks.onSuccess(accumulatedData);
              // 主动关闭连接
              abortController.abort('Stream completed with done event');
            } else {
              // 将done事件的处理推迟，让剩余的文本按照控制的速率展示完
              console.log('[fetchEventSource] Received done event but output queue is not empty. Waiting for queue to drain.');

              // 设置检查定时器，每200ms检查一次队列是否清空
              const checkQueueInterval = setInterval(() => {
                if (outputQueue.value.length === 0 && textBuffer.value === accumulatedData) {
                  clearInterval(checkQueueInterval);
                  agentRequestLoading.value = false;
                  callbacks.onSuccess(accumulatedData);
                  // 主动关闭连接
                  abortController.abort('Stream completed after queue drained');
                  console.log('[fetchEventSource] Output queue is now empty. Completing stream.');
                }
              }, 200);
            }
          } else if (msg.event === 'custom_error' || msg.event === 'error') {
            streamClosed = true;
            agentRequestLoading.value = false;
            const errorMessage = msg.data || 'Server signaled an error via SSE event';
            callbacks.onError(new Error(errorMessage));
            // 主动关闭连接
            abortController.abort('Server sent error event');
          } else {
            console.warn(`[fetchEventSource] Unhandled SSE event type: ${msg.event}`);
          }
        },

        onclose() {
          console.log('[fetchEventSource] Connection closed.');
          if (!streamClosed) {
            // 如果不是因为 done 或 error 正常关闭的
            streamClosed = true;
            agentRequestLoading.value = false;

            // 检查是否是用户主动取消
            if (abortController.signal.aborted) {
              console.log('[fetchEventSource] Connection closed due to abort.');
              // 用户主动取消或收到done事件后的主动关闭
              const abortReason = abortController.signal.reason?.toString() || '';

              if (abortReason.includes('Stream completed') || abortReason.includes('done event')) {
                // 这是正常结束，不需特殊处理
                console.log('[fetchEventSource] Stream was properly terminated');
              } else {
                // 用户取消
                callbacks.onError(new DOMException('请求已取消', 'AbortError'));
              }
            } else if (hasReceivedData) {
              // 已经接收到数据但连接非正常关闭，视为成功结束
              console.warn('[fetchEventSource] Connection closed unexpectedly, but data was received. Treating as success.');
              callbacks.onSuccess(accumulatedData);
            } else {
              // 未收到任何数据就关闭了
              console.error('[fetchEventSource] Connection closed unexpectedly without receiving any data.');
              callbacks.onError(new Error('连接意外关闭，未收到任何数据'));
            }
          }
        },

        onerror(err) {
          console.error('[fetchEventSource] Error:', err);

          if (!streamClosed) {
            streamClosed = true;
            agentRequestLoading.value = false;

            // 如果已收到一些数据但连接出错，视为部分成功
            if (hasReceivedData && accumulatedData) {
              console.warn('[fetchEventSource] Error occurred after receiving data. Treating as partial success.');
              // 这里可以添加一个提示信息
              accumulatedData += '\n\n*注意: 获取完整回复时出现网络中断，上述内容可能不完整。*';
              callbacks.onSuccess(accumulatedData);
            } else {
              // 未收到任何有效数据
              const errorMessage =
                err instanceof Error
                  ? err.name === 'TypeError' && err.message.includes('network')
                    ? '网络连接中断，请检查网络后重试'
                    : err.message
                  : '请求过程中发生错误';

              callbacks.onError(new Error(errorMessage));
            }
          }

          // 对于网络错误或chunked encoding错误，不继续重试
          if (err instanceof TypeError && (err.message.includes('network') || err.message.includes('chunked') || err.message.includes('fetch'))) {
            throw err; // 阻止库的自动重连
          }
        },
      });

      // 返回取消函数
      const cancelRequest = () => {
        if (!streamClosed && currentAbortController === abortController) {
          // 确保取消的是当前请求
          console.log('[fetchEventSource] Cancellation requested.');
          abortController.abort('User canceled request');
          streamClosed = true; // 标记为关闭，防止后续回调执行
          agentRequestLoading.value = false;
          clearOutputProcess(); // 清除输出处理
          currentRequestCancelHandler.value = null; // 清除当前的取消处理函数
        }
      };

      // 存储取消函数以便全局访问
      currentRequestCancelHandler.value = cancelRequest;

      return cancelRequest;
    };

    return [{ request, loading: agentRequestLoading }]; // 返回结构保持一致
  }

  // 在 setup 中调用:
  const [newAgent] = useXAgentWithFetchEventSource(); // 获取新的 agent 实例及其 loading 状态

  // 新的提交函数
  function onSubmitWithNewAgent(nextContent: string) {
    // 1. 检查输入和加载状态 (与原 onSubmit 一致)
    if (!nextContent || newAgent.loading.value) return;

    // 2. 创建并添加用户消息 (与原 onSubmit 一致)
    const newUserMessage: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      role: 'user',
      content: nextContent,
    };
    messages.value.push(newUserMessage);

    // 3. 清空输入框 (与原 onSubmit 一致)
    content.value = '';

    // 4. 滚动到底部 (第一次滚动，显示用户消息)
    forceScrollToBottom(); // 使用强制滚动，因为这是用户发起的操作

    // 重置输出控制
    clearOutputProcess();

    // --- 修正：先准备请求体，再添加界面占位符 ---
    // 7. 准备请求体 (在添加占位符之前获取历史)
    const history = messages.value.slice(-10).map((msg) => ({ role: msg.role, content: msg.content }));
    console.log('[onSubmitWithNewAgent] history for request:', history); // 修改日志标签

    const requestBody: AiSummaryRequestBody = {
      messages: history.filter((msg) => msg.role === 'user' || msg.role === 'assistant') as { role: 'user' | 'assistant'; content: string }[],
      modId: props.modId || '1888072265078280195', // 使用 props 或默认值
    };
    // --- 结束修正 ---

    // 5. 创建 AI 消息占位符 (添加到界面显示)
    const newAiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const newAiMessage: ChatMessage = {
      id: newAiMessageId,
      role: 'assistant',
      content: '', // 初始为空
      loading: true, // 设置为加载中
    };
    messages.value.push(newAiMessage);

    // 6. 再次滚动到底部 (显示 AI 消息占位符)
    forceScrollToBottom(); // 使用强制滚动

    // 8. 调用 newAgent.request 并提供回调来更新 messages ref
    newAgent.request(requestBody, {
      onUpdate: (chunk: string) => {
        // 确保在添加到输出队列前清除所有HTML标签
        const cleanedChunk = chunk.replace(/<br\s*\/?>/g, '\n').replace(/<\/br>/g, '\n');
        // 使用优化后的输出处理机制
        addToOutputQueue(cleanedChunk, newAiMessageId);
      },
      onSuccess: (finalResult: string) => {
        console.log('[New Agent] Success. Final accumulated result (might be redundant if onUpdate handles all):', finalResult);
        // 确保最终结果也没有HTML标签
        const cleanedResult = finalResult.replace(/<br\s*\/?>/g, '\n').replace(/<\/br>/g, '\n');
        
        const msgIndex = messages.value.findIndex((m) => m.id === newAiMessageId);
        if (msgIndex !== -1) {
          messages.value[msgIndex].loading = false;

          // 如果队列中还有内容，快速处理完剩余内容
          if (outputQueue.value.length > 0) {
            // 将所有剩余内容合并并直接设置到消息
            const remainingText = outputQueue.value.map((item) => item.text).join('');
            textBuffer.value += remainingText;
            messages.value[msgIndex].content = textBuffer.value;
            // 清除输出处理
            clearOutputProcess();
          }
          
          // 确保最终消息内容使用清理后的结果
          if (textBuffer.value !== cleanedResult) {
            textBuffer.value = cleanedResult;
            messages.value[msgIndex].content = cleanedResult;
          }

          nextTick(scrollToBottom);
        } else {
          console.warn(`[New Agent Success] AI message with ID ${newAiMessageId} not found!`);
        }
      },
      onError: (error: Error) => {
        console.error('[New Agent] Error:', error);
        // 清除输出处理
        clearOutputProcess();

        const msgIndex = messages.value.findIndex((m) => m.id === newAiMessageId);
        if (msgIndex !== -1) {
          messages.value[msgIndex].loading = false;
          // 检查是否是取消错误
          if (error instanceof DOMException && error.name === 'AbortError') {
            messages.value[msgIndex].content += '\n\n(请求已取消)';
          } else {
            messages.value[msgIndex].content += `\n\n**错误:** ${error.message}`;
          }
          nextTick(scrollToBottom);
        } else {
          console.warn(`[New Agent Error] AI message with ID ${newAiMessageId} not found!`);
          // 可以在这里考虑添加一个新的错误消息到 messages 列表
        }
      },
    });

    // 9. 如果需要提供手动取消功能，可以保存 cancelRequest 函数
    // 例如: currentCancelHandler = cancelRequest;
  }

  const onPromptsItemClick: PromptsProps['onItemClick'] = (info) => {
    if (info.data.description === '重新生成') {
      // 找到最后一条AI消息的索引
      const lastAiMessageIndex = messages.value.findIndex((msg, index) => msg.role === 'assistant' && index === messages.value.length - 1);

      // 确保有AI消息并且是最后一条
      if (lastAiMessageIndex !== -1) {
        // 获取最后一条用户消息索引
        let lastUserMessageIndex = -1;
        for (let i = lastAiMessageIndex - 1; i >= 0; i--) {
          if (messages.value[i].role === 'user') {
            lastUserMessageIndex = i;
            break;
          }
        }

        if (lastUserMessageIndex !== -1) {
          // 删除最后一条AI消息，保留其他所有消息
          messages.value.splice(lastAiMessageIndex, 1);

          // 创建一个新的AI消息占位符
          const newAiMessageId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
          const newAiMessage: ChatMessage = {
            id: newAiMessageId,
            role: 'assistant',
            content: '', // 初始为空
            loading: true, // 设置为加载中
          };
          messages.value.push(newAiMessage);

          // 滚动到底部显示新的AI消息占位符
          forceScrollToBottom();

          // 重置输出控制
          clearOutputProcess();

          // 准备请求体，包含所有历史消息
          const history = messages.value.slice(-10).map((msg) => ({
            role: msg.role,
            content: msg.content,
          }));

          // 过滤掉刚刚添加的空AI消息，避免发送给后端
          const filteredHistory = history.filter((msg) => !(msg.role === 'assistant' && msg.content === ''));

          console.log('[重新生成] 发送历史消息:', filteredHistory);

          const requestBody: AiSummaryRequestBody = {
            messages: filteredHistory as { role: 'user' | 'assistant'; content: string }[],
            modId: props.modId || '1888072265078280195',
          };

          // 直接调用agent请求，而不是通过onSubmitWithNewAgent
          newAgent.request(requestBody, {
            onUpdate: (chunk: string) => {
              // 确保在添加到输出队列前清除所有HTML标签
              const cleanedChunk = chunk.replace(/<br\s*\/?>/g, '\n').replace(/<\/br>/g, '\n');
              // 使用优化后的输出处理机制
              addToOutputQueue(cleanedChunk, newAiMessageId);
            },
            onSuccess: (finalResult: string) => {
              console.log('[重新生成] 成功:', finalResult);
              // 确保最终结果也没有HTML标签
              const cleanedResult = finalResult.replace(/<br\s*\/?>/g, '\n').replace(/<\/br>/g, '\n');
              
              const msgIndex = messages.value.findIndex((m) => m.id === newAiMessageId);
              if (msgIndex !== -1) {
                messages.value[msgIndex].loading = false;

                // 如果队列中还有内容，快速处理完剩余内容
                if (outputQueue.value.length > 0) {
                  // 将所有剩余内容合并并直接设置到消息
                  const remainingText = outputQueue.value.map((item) => item.text).join('');
                  textBuffer.value += remainingText;
                  messages.value[msgIndex].content = textBuffer.value;
                  // 清除输出处理
                  clearOutputProcess();
                }
                
                // 确保最终消息内容使用清理后的结果
                if (textBuffer.value !== cleanedResult) {
                  textBuffer.value = cleanedResult;
                  messages.value[msgIndex].content = cleanedResult;
                }

                nextTick(scrollToBottom);
              }
            },
            onError: (error: Error) => {
              console.error('[重新生成] 错误:', error);
              // 清除输出处理
              clearOutputProcess();

              const msgIndex = messages.value.findIndex((m) => m.id === newAiMessageId);
              if (msgIndex !== -1) {
                messages.value[msgIndex].loading = false;
                // 检查是否是取消错误
                if (error instanceof DOMException && error.name === 'AbortError') {
                  messages.value[msgIndex].content += '\n\n(请求已取消)';
                } else {
                  messages.value[msgIndex].content += `\n\n**错误:** ${error.message}`;
                }
                nextTick(scrollToBottom);
              }
            },
          });
        }
      } else {
        // 没有找到最后一条AI消息，直接将重新生成作为新消息发送
        onSubmitWithNewAgent(info.data.description as string);
      }
    } else {
      // 其他类型的提示按钮，直接发送
      onSubmitWithNewAgent(info.data.description as string);
    }
  };

  function renderMarkdown(markdownContent: string): string {
    if (!markdownContent) return '';
    try {
      const rawHtml = marked.parse(markdownContent, { breaks: true }) as string;
      const cleanHtml = DOMPurify.sanitize(rawHtml);
      return cleanHtml;
    } catch (error) {
      console.error('Markdown rendering error:', error);
      return markdownContent; // 出错时返回原始内容
    }
  }

  // 监听用户滚动事件
  function setupScrollListener() {
    if (messagesContainerRef.value) {
      messagesContainerRef.value.addEventListener('scroll', handleUserScroll);
    }
  }

  // 当组件卸载时移除事件监听器
  function cleanupScrollListener() {
    if (messagesContainerRef.value) {
      messagesContainerRef.value.removeEventListener('scroll', handleUserScroll);
    }
  }

  // 处理用户滚动事件
  function handleUserScroll() {
    if (!messagesContainerRef.value) return;

    const container = messagesContainerRef.value;
    const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight < scrollThreshold;

    // 标记用户滚动意图
    userScrolled.value = true;

    // 如果用户滚动到接近底部，重新启用自动滚动
    if (isAtBottom) {
      autoScrollEnabled.value = true;
    } else {
      autoScrollEnabled.value = false;
    }
  }

  // 修改滚动到底部函数以尊重用户意图
  function scrollToBottom() {
    nextTick(() => {
      if (messagesContainerRef.value && autoScrollEnabled.value) {
        messagesContainerRef.value.scrollTop = messagesContainerRef.value.scrollHeight;
      }
    });
  }

  // 强制滚动到底部，无视用户意图（用于特定情况如用户发送新消息）
  function forceScrollToBottom() {
    nextTick(() => {
      if (messagesContainerRef.value) {
        messagesContainerRef.value.scrollTop = messagesContainerRef.value.scrollHeight;
        // 用户已主动发送了新消息，重新启用自动滚动
        autoScrollEnabled.value = true;
      }
    });
  }

  // 在组件挂载后设置滚动监听器
  onMounted(() => {
    setupScrollListener();
  });

  // 在组件卸载前清理滚动监听器
  onUnmounted(() => {
    cleanupScrollListener();
  });

  function getLastAiMessageContent() {
    const lastAiMessage = messages.value
      .slice()
      .reverse()
      .find((msg) => msg.role === 'assistant');
    return lastAiMessage ? lastAiMessage.content : 'No AI messages yet.';
  }

  defineExpose({
    getLastAiMessageContent,
  });

  // 速率调整UI状态和实际控制值
  const showSpeedSettings = ref(false);
  const outputSpeed = ref(props.defaultOutputSpeed);
  const sentenceDelay = ref(props.defaultSentenceDelay);
  const paragraphDelay = ref(props.defaultParagraphDelay);

  // 当前UI设置的临时值
  const currentOutputSpeed = ref(props.defaultOutputSpeed);
  const currentSentenceDelay = ref(props.defaultSentenceDelay);
  const currentParagraphDelay = ref(props.defaultParagraphDelay);

  // LocalStorage键名
  const STORAGE_KEYS = {
    OUTPUT_SPEED: 'physicalex-chat-output-speed',
    SENTENCE_DELAY: 'physicalex-chat-sentence-delay',
    PARAGRAPH_DELAY: 'physicalex-chat-paragraph-delay',
  };

  // 将设置保存到LocalStorage
  function saveSettingsToStorage() {
    try {
      localStorage.setItem(STORAGE_KEYS.OUTPUT_SPEED, outputSpeed.value.toString());
      localStorage.setItem(STORAGE_KEYS.SENTENCE_DELAY, sentenceDelay.value.toString());
      localStorage.setItem(STORAGE_KEYS.PARAGRAPH_DELAY, paragraphDelay.value.toString());
      console.log('保存设置到LocalStorage成功');
    } catch (e) {
      console.error('保存设置到LocalStorage失败:', e);
    }
  }

  // 从LocalStorage加载设置
  function loadSettingsFromStorage() {
    try {
      const storedOutputSpeed = localStorage.getItem(STORAGE_KEYS.OUTPUT_SPEED);
      const storedSentenceDelay = localStorage.getItem(STORAGE_KEYS.SENTENCE_DELAY);
      const storedParagraphDelay = localStorage.getItem(STORAGE_KEYS.PARAGRAPH_DELAY);

      // 如果存在存储的值，则使用它们
      if (storedOutputSpeed !== null) {
        outputSpeed.value = parseInt(storedOutputSpeed);
      }
      if (storedSentenceDelay !== null) {
        sentenceDelay.value = parseInt(storedSentenceDelay);
      }
      if (storedParagraphDelay !== null) {
        paragraphDelay.value = parseInt(storedParagraphDelay);
      }

      console.log('从LocalStorage加载设置成功');
    } catch (e) {
      console.error('从LocalStorage加载设置失败:', e);
      // 加载失败时使用默认值
      resetToDefaultSettings();
    }
  }

  // 重置为默认设置，但不保存
  function resetToDefaultSettings() {
    outputSpeed.value = props.defaultOutputSpeed;
    sentenceDelay.value = props.defaultSentenceDelay;
    paragraphDelay.value = props.defaultParagraphDelay;
  }

  // 计算属性：是否正在使用自定义设置
  const isUsingCustomSettings = computed(() => {
    return (
      outputSpeed.value !== props.defaultOutputSpeed ||
      sentenceDelay.value !== props.defaultSentenceDelay ||
      paragraphDelay.value !== props.defaultParagraphDelay
    );
  });

  // 清除所有保存的设置
  function clearAllSettings() {
    try {
      localStorage.removeItem(STORAGE_KEYS.OUTPUT_SPEED);
      localStorage.removeItem(STORAGE_KEYS.SENTENCE_DELAY);
      localStorage.removeItem(STORAGE_KEYS.PARAGRAPH_DELAY);
      console.log('已清除所有保存的设置');
    } catch (e) {
      console.error('清除设置失败:', e);
    }
  }

  // 应用速率设置
  function applySpeedSettings() {
    // 从UI控件获取当前值应用到实际控制值
    outputSpeed.value = currentOutputSpeed.value;
    sentenceDelay.value = currentSentenceDelay.value;
    paragraphDelay.value = currentParagraphDelay.value;

    // 保存到localStorage
    saveSettingsToStorage();
  }

  // 重置为默认值并清除保存的设置
  function resetSpeedSettings() {
    currentOutputSpeed.value = props.defaultOutputSpeed;
    currentSentenceDelay.value = props.defaultSentenceDelay;
    currentParagraphDelay.value = props.defaultParagraphDelay;
    applySpeedSettings();
    clearAllSettings(); // 清除所有保存的设置
  }

  // 应用设置并关闭抽屉
  function applyAndCloseSettings() {
    applySpeedSettings();
    showSpeedSettings.value = false;
  }

  // 取消并关闭抽屉
  function cancelSettings() {
    // 恢复到当前的实际值
    currentOutputSpeed.value = outputSpeed.value;
    currentSentenceDelay.value = sentenceDelay.value;
    currentParagraphDelay.value = paragraphDelay.value;
    showSpeedSettings.value = false;
  }

  // 初始化UI控件的值
  function initSpeedSettings() {
    // 尝试从localStorage加载设置
    loadSettingsFromStorage();

    // 同步UI控件值
    currentOutputSpeed.value = outputSpeed.value;
    currentSentenceDelay.value = sentenceDelay.value;
    currentParagraphDelay.value = paragraphDelay.value;
  }

  // 初始化UI设置值
  initSpeedSettings();

  // 手动停止当前请求
  function stopCurrentRequest() {
    console.log('手动停止请求被触发', currentRequestCancelHandler.value ? '有取消函数' : '无取消函数');

    if (currentRequestCancelHandler.value) {
      // 调用取消处理函数
      currentRequestCancelHandler.value();

      // 添加停止消息到最后一条AI消息
      const lastAiMessageIndex = messages.value.findIndex((msg) => msg.role === 'assistant' && msg.loading);

      if (lastAiMessageIndex !== -1) {
        messages.value[lastAiMessageIndex].loading = false;
        messages.value[lastAiMessageIndex].content += '\n\n*（已手动停止生成）*';
        nextTick(scrollToBottom);
      }

      // 强制重置状态
      setTimeout(() => {
        if (newAgent.loading.value) {
          console.log('强制重置loading状态');
          newAgent.loading.value = false;
        }
      }, 100);
    }
  }

  function handleClose() {
    console.log('关闭按钮被触发');
    alert();
    // 这里可以添加关闭逻辑，比如关闭对话框或执行其他操作
  }

  // 定义自定义事件
  const emit = defineEmits(['adoptAdvice']);

  function handleAdoptMessage(message: ChatMessage) {
    if (message.role === 'assistant' && !message.loading) {
      // 将Markdown内容打包成JSON
      const jsonData = {
        content: message.content,
        parsedContent: [
          {
            "advice": [
              {
                "seq": 1,
                "name": "异常项目名称或综合建议",
                "content": message.content,
                "diagnosticCriteria": "医学诊断依据",
                "refAbnormalSeq": 1
              }
            ]
          }
        ]
      };
      
      // 通过自定义事件抛出
      emit('adoptAdvice', jsonData);
    }
  }
</script>

<template>
  <div :style="styles.layout">
    <div :style="styles.chat">
      <div ref="messagesContainerRef" :style="styles.messagesContainer">
        <div v-for="message in messages" :key="message.id" :style="styles.messageItem">
          <div
            :style="[styles.messageContent, message.role === 'user' ? styles.messageContentUser : styles.messageContentAssistant]"
            v-html="renderMarkdown(message.content)"
          ></div>
          <span v-if="message.role === 'assistant' && message.loading" :style="styles.loadingIndicator">...</span>
          
          <!-- 为AI消息添加操作按钮区域 -->
          <div v-if="message.role === 'assistant' && !message.loading" :style="styles.messageActions">
            <Button 
              type="primary" 
              size="small" 
              @click="handleAdoptMessage(message)"
              :style="styles.actionButton"
            >
              <template #icon><CheckOutlined /></template>
              采用
            </Button>
          </div>
        </div>
        <div v-if="messages.length === 0" :style="styles.placeholder"> 可以开始提问了... </div>
      </div>

      <!-- Prompts和控制按钮在同一行 -->
      <div :style="styles.promptsRow">
        <div :style="styles.promptsContainer">
          <Prompts :items="senderPromptsItems" @item-click="onPromptsItemClick" />
        </div>

        <div :style="styles.controlsContainer">
          <Tooltip :title="`调整输出速率设置${isUsingCustomSettings ? ' (已使用自定义设置)' : ''}`">
            <Button type="text" shape="circle" size="small" @click="showSpeedSettings = true">
              <template #icon><SettingOutlined /></template>
            </Button>
          </Tooltip>
        </div>
      </div>

      <Sender
        :value="content"
        :style="styles.senderCompact"
        :loading="newAgent.loading.value"
        @submit="onSubmitWithNewAgent"
        @change="(value) => (content = value)"
        @cancel="stopCurrentRequest"
        placeholder="请输入您的问题..."
      />
    </div>

    <!-- 速率设置抽屉 -->
    <Drawer
      title="输出速率设置"
      placement="right"
      :closable="true"
      :visible="showSpeedSettings"
      @close="cancelSettings"
      :width="280"
      :bodyStyle="{ padding: '12px 8px' }"
    >
      <div v-if="isUsingCustomSettings" :style="{ marginBottom: '8px', fontSize: '12px', color: '#1890ff' }"> 您正在使用自定义设置 </div>

      <div :style="{ marginBottom: '8px' }">
        <div :style="{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }">
          <span :style="{ fontSize: '14px', fontWeight: 'bold' }">输出速度</span>
          <span :style="{ fontSize: '12px', color: '#8c8c8c' }">{{ currentOutputSpeed }} 字/秒</span>
        </div>
        <Slider v-model:value="currentOutputSpeed" :min="10" :max="100" :step="5" :tooltip="{ formatter: (v) => `${v} 字/秒` }" />
      </div>

      <div :style="{ marginBottom: '8px' }">
        <div :style="{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }">
          <span :style="{ fontSize: '14px', fontWeight: 'bold' }">句子间停顿</span>
          <span :style="{ fontSize: '12px', color: '#8c8c8c' }">{{ currentSentenceDelay }} 毫秒</span>
        </div>
        <Slider v-model:value="currentSentenceDelay" :min="0" :max="1000" :step="50" :tooltip="{ formatter: (v) => `${v} 毫秒` }" />
      </div>

      <div :style="{ marginBottom: '12px' }">
        <div :style="{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }">
          <span :style="{ fontSize: '14px', fontWeight: 'bold' }">段落间停顿</span>
          <span :style="{ fontSize: '12px', color: '#8c8c8c' }">{{ currentParagraphDelay }} 毫秒</span>
        </div>
        <Slider v-model:value="currentParagraphDelay" :min="200" :max="2000" :step="100" :tooltip="{ formatter: (v) => `${v} 毫秒` }" />
      </div>

      <div :style="{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }">
        <Button size="small" @click="resetSpeedSettings">重置默认值</Button>
        <Button size="small" type="primary" @click="applyAndCloseSettings">应用设置</Button>
      </div>
    </Drawer>
  </div>
</template>
