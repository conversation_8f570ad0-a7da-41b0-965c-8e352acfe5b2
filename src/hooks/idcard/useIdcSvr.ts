import { reactive, onMounted } from 'vue';
import type { IdcSvr, IdcData } from '#/utils';

export function createIdcData(): IdcData {
  return reactive<IdcData>({
    data: {},
    ok: false,
    msg: '',
    state: '',
    action: '',
  });
}

export function useIdcSvr() {
  const idcData = createIdcData();
  // 优化：通过 window 访问全局变量，并增加存在性检查
  const idcSvr: IdcSvr = (window as any).clsIdCardSvr?.createNew?.();
  if (!idcSvr) {
    throw new Error('身份证识别库未加载或未正确初始化');
  }
  const send = (cmd) => {
    idcSvr.send(cmd);
  };

  const handleIdcardInit = () => {
    send({
      License: `${window._CONFIG['idSvrLicense']}`,
      AutoStopTime: '3600',
      PicFormat: 'png',
      PicBgColor: '00FFFFFF',
      Ports: 'USB1,*',
      ComEnabled: 'false',
      Verify: '',
      SetVoice: 'VW Liang, Chinese, China, 0',
      SetVoiceRate: '1',
      LocalFunctionEnable: 'false',
    });
  };

  const handleIdcardRead = (data: Record<string, any>) => {
    console.log('handleIdcardRead', data);
    idcData.data = data;
    idcData.ok = true;
  };

  const handleIdcardMessage = (msg: string) => {
    idcData.msg = msg;
  };

  const handleIdcardState = (state: string, msg: string) => {
    idcData.state = state;
    idcData.msg = msg;
  };

  const handleIdcardResult = (result: string) => {
    idcData.msg = result;
  };

  onMounted(() => {
    idcSvr.debug(0);
    idcSvr.event = {
      oninit: handleIdcardInit,
      onread: handleIdcardRead,
      onmessage: handleIdcardMessage,
      onstate: handleIdcardState,
      onresult: handleIdcardResult,
    };

    idcSvr.connect(`ws://127.0.0.1:${window._CONFIG['idSvrPort']}`);
  });

  return {
    idcData,
    send,
  };
}
